FROM 656952484900.dkr.ecr.ap-south-1.amazonaws.com/devops/node-alpine:build-v13.8.0-ssh-s3 AS builder
WORKDIR /root/.ssh
RUN echo "StrictHostKeyChecking no" > config
WORKDIR /var/www/app
COPY . .
RUN npm install
# RUN npm run sonar-scanner -Dsonar.projectKey=stocks-mini -Dsonar.sources=. -Dsonar.host.url=https://sonarqube-dashboard.paytmmoney.com -Dsonar.login=****************************************
# RUN npm run sonar-scanner
RUN npm run build
ARG bucket_name
RUN aws s3 cp build  s3://$bucket_name/ --recursive
