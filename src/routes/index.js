import buybackRoute from '../buyback/routes';
import delistingRoutes from '../delisting/routes';
import {
  festiveTradingRoute,
  lotusTradingRoute,
  lotusTradingCommonRoute,
  muhuratTradingRoute,
} from '../LotusTrading/routes';
import offerForSaleRoutes from '../offerForSale/routes';
import merchantRoutes from '../merchant/routes';
import URL from './config/urlConfig';
import taxHarvestingRoute from '../TaxHarvesting/routes';
import sipCalculatorRoute from '../SIPCalculator/routes';
import SIP_ROUTES from '../SIPRecommendation/routes';
import mtfRoute from '../mtf/routes';
import mtfCalculatorRoute from '../mtf-calculator/routes';
import hotStocksRoutes from '../HotStocks/routes';
import trendingStocksRoutes from '../TrendingStocks/routes';
import companyBreakoutRoutes from '../CompanyBreakout/routes';
import dematHoldingRoutes from '../DematHoldings/routes';

// The top-level (parent) route
const routes = {
  path: URL.ROOT,

  // Keep in mind, routes are evaluated in order
  children: [
    {
      path: '',
      load: () => import(/* webpackChunkName: 'Landing' */ './Landing'),
    },
    {
      path: URL.LANDING,
      load: () => import(/* webpackChunkName: 'Landing' */ './Landing'),
    },
    {
      path: URL.STOCKS,
      load: () => import(/* webpackChunkName: 'equities' */ './Equities'),
    },
    {
      path: URL.ROOT,
      children: buybackRoute.concat(
        delistingRoutes,
        offerForSaleRoutes,
        merchantRoutes,
        sipCalculatorRoute,
      ),
    },
    {
      path: URL.CORPORATE_ACTIONS,
      children: [
        {
          path: '',
          load: () => import(/* webpackChunkName: 'Landing' */ './Landing'),
        },
        {
          path: URL.LANDING,
          load: () => import(/* webpackChunkName: 'Landing' */ './Landing'),
        },
      ].concat(
        buybackRoute,
        delistingRoutes,
        offerForSaleRoutes,
        sipCalculatorRoute,
      ),
    },
    {
      path: URL.LOTUS_TRADING,
      children: lotusTradingRoute,
    },
    {
      path: URL.LOTUS_TRADING_WEB,
      children: lotusTradingCommonRoute,
    },
    {
      path: URL.FESTIVE_TRADING_IDEAS,
      children: festiveTradingRoute,
    },
    { path: URL.MUHURAT_TRADING, children: muhuratTradingRoute },
    {
      path: URL.MARKET_UPDATES,
      load: () =>
        import(/* webpackChunkName: 'market-updates' */ './MarketUpdates'),
    },
    {
      path: URL.TAX_HARVESTING,
      children: taxHarvestingRoute,
    },
    {
      path: URL.FNO_TNC,
      load: () => import(/* webpackChunkName: 'fno-tnc' */ '../FnOTnC'),
    },
    {
      path: URL.SIP_RECOMMENDATIONS,
      children: SIP_ROUTES,
    },
    {
      path: URL.MTF,
      children: mtfRoute,
    },
    {
      path: URL.MTF_CALCULATOR,
      children: mtfCalculatorRoute,
    },
    {
      path: URL.COMPANY_BREAKOUT,
      children: companyBreakoutRoutes,
    },
    {
      path: URL.ERROR_PAGE,
      load: () => import(/* webpackChunkName : 'ErrorPage' */ './ErrorPage'),
    },
    {
      path: URL.HOT_STOCKS,
      children: hotStocksRoutes,
    },
    {
      path: URL.TRENDING_STOCKS,
      children: trendingStocksRoutes,
    },
    {
      path: URL.DEMAT_HOLDINGS,
      children: dematHoldingRoutes,
    },
  ],
  async action({ next }) {
    // Execute each child route until one of them return the result
    let route = await next();
    route = route || {};

    route.title = route.title || '';
    route.description = route.description || '';

    return route;
  },
};

// The error page is available by permanent url for development mode
if (__DEV__) {
  /**
   * @TODO remove comment after __DEV__ issue,
   */
  // routes.children.unshift({
  //   path: '/error',
  //   action: require('./ErrorPage').default,
  // });
}

export default routes;
