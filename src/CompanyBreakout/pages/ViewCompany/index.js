import classNames from 'classnames'

import React, { useEffect, useRef, useState } from 'react'
import { getBreakoutDetails, getBreakoutJSON } from '../../../actions/companyBreakoutActions'
import { useDrawer } from '../../../components/Drawer'
import history from '../../../history'
import baseComponent from '../../../HOC/BaseComponent/BaseComponent'
import HeaderLayout from '../../../layout/HeaderLayout/HeaderLayout'
import { goBack, setDarkModeValue } from '../../../services/coreUtil'
import { getAppTheme, openInBrowser, paytmChangeBottomBarColorBridge, paytmChangeStatusBarColorBridge } from '../../../utils/bridgeUtils'
import { getDeeplinkDataOrQueryParam, isDarkMode, isIosBuild } from '../../../utils/commonUtils'
import { useBackPress } from '../../../utils/react'
import BreakoutHeader from '../../component/BreakoutHeader/BreakoutHeader'
import DrawerContent from '../../component/DrawerContent'
import NoData from '../../component/NoData'
import ShimmerLoading from '../../component/ShimmerLoading'
import SwipeableTabs from '../../component/SwipeableTabs'
import TimelineList from '../../component/TimelineList'
import { ALL_BREAKOUTS_SUBTITLE, BREAKOUT_TABS, COMPANY_BREAKOUT_EVENTS } from '../../utils/enums'
import { sendBreakoutAnalyticsEvent } from '../../utils/utilFn'
import styles from './index.scss'
import DeviceInfoProvider from '../../../utils/Providers/DeviceInfoProvider'

const CompanyBreakout = (props) => {
  const loadingref = useRef();
  const listContainerRef = useRef(null);
  const { pushStack, clearStack } = useBackPress();
  const [darkModeState, setDarkModeState] = useState(false);

  // URL for iOS: 'https://h5-stocks.paytmmoney.com/company-breakout/view?pmlId=**********?origin=PAYTMMONEY&os=ios&darkmode=true'
  const params = {
    pmlId: isIosBuild()
      ? getDeeplinkDataOrQueryParam('pmlId')?.split('?')[0]
      : getDeeplinkDataOrQueryParam('pmlId'),
    darkMode: isIosBuild()
      ? window.location.search
          ?.split('?')
          .pop()
          ?.split('&')
          .find(param => param.startsWith('darkmode='))
          ?.split('=')[1]
      : getDeeplinkDataOrQueryParam('darkmode'),
    name: getDeeplinkDataOrQueryParam('name'),
  };
  console.log('query params', params);

  const [activeTab, setActiveTab] = useState(0);
  const [data, setData] = useState(null);
  const [rawData, setRawData] = useState([]);
  const [breakoutJSON, setBreakoutJSON] = useState([]);
  const [breakoutDetails, setBreakoutDetails] = useState(null);
  const [hasMore, setHasMore] = useState(true);
  const [saKeys, setSaKeys] = useState([]);
  const [loading, setLoading] = useState(true);
  const [isScrolled, setIsScrolled] = useState(false);


  const { isOpen, onToggle, onClose } = useDrawer()

  const onInfoClick = (id) => {
    const details = breakoutJSON.find(item => item.breakoutId === id);
    setBreakoutDetails(details);
    onToggle()
  }

  const onLearnMoreClick = () => {
    openInBrowser(breakoutDetails?.cta);
  }

  useEffect(() => {
    try {
      getAppTheme(res => {
        const isDarkModeEnabled =
          res?.data?.isNightModeEnabled === true || params?.darkMode === 'true';
        console.log('res :: ', res, 'isDarkModeEnabled :: ', isDarkModeEnabled);
        setDarkModeState(isDarkModeEnabled);
        setDarkModeValue(isDarkModeEnabled);
        DeviceInfoProvider.setInfo('origin', 'PAYTMMONEY');
      });
    } catch (e) {
      console.log('error: getAppTheme', e);
    }
  }, []);

  useEffect(() => {
    setLoading(true);
    const utmSource = sessionStorage.getItem('utm_source');
    sendBreakoutAnalyticsEvent(COMPANY_BREAKOUT_EVENTS.COMPANY_PAGE_SCREEN_NAME, {
      event_action: COMPANY_BREAKOUT_EVENTS.COMPANY_PAGE_SCREEN_NAME,
      event_label4: utmSource,
      event_label2: params.name,
    });
    getBreakoutJSON().then(res => {
      setBreakoutJSON(res.data);
    })
  }, [])

  useEffect(() => {
    if (params && params.tab) {
      const position = BREAKOUT_TABS.findIndex(tab => tab.id === params.tab)
      setActiveTab(position > 0 ? position : 0)
    } else {
      setActiveTab(0)
    }
  }, [])

  const getData = async (loadMore = hasMore, q, loadMoreParams = saKeys, prevData = rawData) => {
    if (!loadMore) return;
    let queryParams = `?pmlId=${params.pmlId}&pageSize=10`
    if (q) {
      queryParams += `&${q}`
    }

    if (loadMoreParams.length) {
      queryParams += `&saKeys=${loadMoreParams}`
    }

    const response = await getBreakoutDetails(queryParams)
    if (response.data.saKeys.length) {
      setSaKeys(response.data.saKeys);
    }

    if (response.data.saKeys.length === 0) {
      setHasMore(false);
      setSaKeys(response.data.saKeys);
      return;
    }

    setRawData(() => [...prevData, ...response.data.results]);

    const combinedData = [...prevData, ...response.data.results].reduce((acc, curr) => {
      const key = curr.bTime.slice(0, -3);
      if (!acc[key]) {
        acc[key] = []
      }
      acc[key].push(curr)
      return acc
    }, {})

    setData(combinedData)
  }

  const getInitialData = async (...args) => {
    setLoading(true);
    await getData(...args);
    setLoading(false);
  }

  const query =
    activeTab === 0
      ? ''
      : `${BREAKOUT_TABS[activeTab].key}=${BREAKOUT_TABS[activeTab].value}`;

  useEffect(() => {
    setHasMore(true);
    setSaKeys([]);
    setData([]);
    setRawData([]);
    getInitialData(true, query, [], []);
  }, [activeTab]);

  console.log('LOADING', loading);

  useEffect(() => {
    const activeTab = getDeeplinkDataOrQueryParam('activeTab');
    if (activeTab) {
      setActiveTab(Number(activeTab));
    }

  }, []);

  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        const target = entries[0];
        if (target.isIntersecting && hasMore) {
          if (loading) {
            return;
          }

          getData(hasMore, query, saKeys, rawData);
        }
      },
      { threshold: 0.1 }
    );
    if (loadingref.current) {
      observer.observe(loadingref.current);
    }
    return () => {
      if (loadingref.current) {
        observer.unobserve(loadingref.current);
      }
    };
  }, [saKeys, loading, hasMore, rawData]);

  useEffect(() => {
    const style = getComputedStyle(document.body);
    paytmChangeBottomBarColorBridge(
      style.getPropertyValue('--plain-background'),
    );
    setTimeout(() => {
      paytmChangeStatusBarColorBridge(
        style.getPropertyValue('--plain-background'),
      );
    }, 100);
  }, []);

  const headerProps = {
    hideHeader: true,
  }

  const handleRetry = () => { }

  const handleBackPress = () => {
    goBack(history);
  };

  const paginationLoader = () => <div ref={loadingref}><ShimmerLoading /></div>;
  return (
    <HeaderLayout {...props} {...headerProps} helperIconClick={handleRetry}>
      <div
        className={classNames(styles.container, {
          [styles.containerLight]: !darkModeState,
        })}
      >
        <BreakoutHeader
          title={params?.name}
          subTitle={ALL_BREAKOUTS_SUBTITLE}
          handleBackPress={handleBackPress}
          onScroll={(value) => setIsScrolled(value)}
        />
        <SwipeableTabs
          isEmpty={!data}
          tabs={BREAKOUT_TABS}
          activeTab={activeTab}
          setActiveTab={setActiveTab}
          isScrolled={isScrolled}
          content={BREAKOUT_TABS.map(tab => ({
            id: tab.id,
            children: (
              <div className={styles.list}>
                {loading ? <div /> : Object.keys(data || {}).length ? <TimelineList data={data} onInfoClick={onInfoClick} getData={getData} hasMore={hasMore} scrollRef={listContainerRef} breakoutJSON={breakoutJSON} /> : <div className={styles.noData}><NoData /></div>}
              </div>
            ),
          }))}
        />
        {hasMore ? paginationLoader() : null}
      </div>

      <DrawerContent isOpen={isOpen} description={breakoutDetails?.longDesc} title={breakoutDetails?.breakoutName} onClose={onClose} darkModeState={darkModeState} />

    </HeaderLayout>
  )
}

export default baseComponent(CompanyBreakout);
