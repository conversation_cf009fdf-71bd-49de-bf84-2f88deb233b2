import classNames from 'classnames'
import React, { useCallback, useEffect, useRef, useState } from 'react'
import { getBreakoutJSON, getLatestBreakouts } from '../../../actions/companyBreakoutActions'
import { useDrawer } from '../../../components/Drawer'
import history from '../../../history'
import baseComponent from '../../../HOC/BaseComponent/BaseComponent'
import HeaderLayout from '../../../layout/HeaderLayout/HeaderLayout'
import { navigateTo } from '../../../services/coreUtil'
import { exitApp, openInBrowser, paytmChangeBottomBarColorBridge, paytmChangeStatusBarColorBridge } from '../../../utils/bridgeUtils'
import { getDeeplinkDataOrQueryParam, isDarkMode } from '../../../utils/commonUtils'
import { useBackPress } from '../../../utils/react'
import Header from '../../component/BreakoutHeader/BreakoutHeader'
import Card from '../../component/Card'
import DrawerContent from '../../component/DrawerContent'
import Filters from '../../component/FiltersTab'
import NoData from '../../component/NoData'
import ShimmerLoading from '../../component/ShimmerLoading'
import SwipeableTabs from '../../component/SwipeableTabs'
import { ALL_BREAKOUTS, ALL_BREAKOUTS_SUBTITLE, COMPANY_BREAKOUT_EVENTS, FILTER_LIST, HOME_BREAKOUT_TABS } from '../../utils/enums'
import { sendBreakoutAnalyticsEvent } from '../../utils/utilFn'
import styles from './index.scss'

const CompanyBreakout = (props) => {
  const loadingref = useRef();
  const { pushStack, clearStack } = useBackPress();

  const [data, setData] = useState([]);
  const [activeTab, setActiveTab] = useState(0);
  const [activeSubTab, setActiveSubTab] = useState(null);
  const [breakoutJSON, setBreakoutJSON] = useState([]);
  const [breakoutDetails, setBreakoutDetails] = useState(null);
  const [saKeys, setSaKeys] = useState([]);
  const [hasMore, setHasMore] = useState(true);
  const [loading, setLoading] = useState(false);


  const { isOpen, onOpen, onClose } = useDrawer()

  const headerProps = {
    hideHeader: true,
  }

  const handleRetry = () => { }

  const getData = async (q, moreData = hasMore, lastData = data, loadMoreParams = saKeys,) => {
    if (!moreData) {
      return;
    }
    let query = '?pageSize=10';
    if (q) {
      query += `&${q}`;
    }
    if (loadMoreParams.length) {
      query += `&saKeys=${loadMoreParams}`
    }
    const response = await getLatestBreakouts(query);
    if (response?.data?.saKeys?.length) {
      setSaKeys(response.data.saKeys);
    }

    if (Object.keys(response.data || {}).length === 0) {
      setHasMore(false);
      setSaKeys([]);
      return;
    }

    if (response?.data?.saKeys?.length === 0) {
      setHasMore(false);
      setSaKeys(response.data.saKeys);
      return;
    }
    if (response?.data?.results) {
      setData(() => [...lastData, ...response.data.results]);
    }
  }

  const onInfoClick = ({ bId, ...item }) => {
    const details = breakoutJSON.find(item => item.breakoutId === bId);
    sendBreakoutAnalyticsEvent(COMPANY_BREAKOUT_EVENTS.ALL_BREAKOUT_SCREEN_NAME, {
      event_action: COMPANY_BREAKOUT_EVENTS.ALL_BREAKOUT_INFO_ICON_CLICK,
      event_label2: item.name,
      event_label3: bId,
      event_label4: details.breakoutName,
      event_label5: item.bType.join(),
      event_label6: item.bTrend,
    });
    setBreakoutDetails(details);
    onOpen();
  }

  const getInitialData = async (q) => {
    setLoading(true);
    await getData(q, true, [], []);
    setLoading(false);
  }


  const onFilterClick = async (item) => {
    sendBreakoutAnalyticsEvent(COMPANY_BREAKOUT_EVENTS.ALL_BREAKOUT_SCREEN_NAME, {
      event_action: COMPANY_BREAKOUT_EVENTS.ALL_BREAKOUT_FILTER_CLICK,
      event_label2: item.name,
      event_category: COMPANY_BREAKOUT_EVENTS.ALL_BREAKOUT_VERTICAL_NAME,
      vertical_name: COMPANY_BREAKOUT_EVENTS.ALL_BREAKOUT_VERTICAL_NAME
    });
  }

  const onCardClick = (e, { breakoutName, ...item }) => {
    e.stopPropagation();
    sendBreakoutAnalyticsEvent(COMPANY_BREAKOUT_EVENTS.ALL_BREAKOUT_SCREEN_NAME, {
      event_action: COMPANY_BREAKOUT_EVENTS.CLICKED_EVENT_ACTION,
      event_label2: item.name,
      event_label3: item.bId,
      event_label4: breakoutName,
      event_label5: item.bType.join(),
      event_label6: item.bTrend,
      event_category: COMPANY_BREAKOUT_EVENTS.ALL_BREAKOUT_VERTICAL_NAME,
      vertical_name: COMPANY_BREAKOUT_EVENTS.ALL_BREAKOUT_VERTICAL_NAME
    })
    navigateToViewPage(item);
  }

  useEffect(() => {
    const utmSource = sessionStorage.getItem('utm_source');
    sendBreakoutAnalyticsEvent(COMPANY_BREAKOUT_EVENTS.ALL_BREAKOUT_SCREEN_NAME, {
      event_action: COMPANY_BREAKOUT_EVENTS.ALL_BREAKOUT_LANDING_PAGE,
      event_label: utmSource,
    });
    getBreakoutJSON().then(res => {
      setBreakoutJSON(res.data);
    })
  }, [])

  useEffect(() => {
    const activeTab = getDeeplinkDataOrQueryParam('activeTab');
    if (activeTab) {
      setActiveSubTab(Number(activeTab));
    } else {
      setActiveSubTab(0);
    }
  }, []);

  useEffect(() => {
    setHasMore(true);
    setSaKeys([]);
    setData([]);
    if (activeSubTab !== null) {
      const item = FILTER_LIST[activeSubTab];
      if (item.value) {
        getInitialData(`${item.key}=${item.value}`);
      } else {
        getInitialData();
      }  
    }

  }, [activeSubTab]);

  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        const target = entries[0];
        if (target.isIntersecting && hasMore) {
          if (loading) {
            return;
          }
          const item = FILTER_LIST[activeSubTab];

          if (item.value) {
            getData(`${item.key}=${item.value}`, hasMore, data, saKeys);
          } else {
            getData();
          }
        }
      },
      { threshold: 0.1 }
    );
    if (loadingref.current) {
      observer.observe(loadingref.current);
    }
    return () => {
      if (loadingref.current) {
        observer.unobserve(loadingref.current);
      }
    };
  }, [saKeys, loading]);

  const Content = useCallback(() => {
    return <div className={styles.list}>
        {loading ? <div /> : data.length ? data.map((item) => {
          const breakoutName = breakoutJSON.find(breakout => breakout.breakoutId === item?.bId)?.breakoutName || '';
          return <div className={styles.card} onClick={(e) => onCardClick(e, { breakoutName, ...item, })} key={item.pmlId}><Card title={breakoutName} description={breakoutJSON.find(breakout => breakout.breakoutId === item?.bId)?.shortDesc || ''} {...item} onInfoClick={(bId) => onInfoClick({ bId, ...item })} /></div>;
        }) : <div className={styles.noData}><NoData /></div>}
    </div>
  }, [data, breakoutJSON, loading]);

  const navigateToViewPage = (item) => {
    navigateTo(history, `view?pmlId=${item.pmlId}&name=${item.name}`);
  }

  const onLearnMoreClick = () => {
    openInBrowser(breakoutDetails?.cta);
  }

  const handleBackPress = () => {
    exitApp();
  };
  const paginationLoader = () => <div ref={loadingref}><ShimmerLoading /></div>;

  useEffect(() => {
    pushStack(handleBackPress);
    return () => {
      clearStack();
    };
  }, []);

  useEffect(() => {
    const style = getComputedStyle(document.body);
    paytmChangeBottomBarColorBridge(
      style.getPropertyValue('--plain-background'),
    );
    setTimeout(() => {
      paytmChangeStatusBarColorBridge(
        style.getPropertyValue('--plain-background'),
      );
    }, 100);
  }, []);

  return (
    <>
    <HeaderLayout {...props} {...headerProps} helperIconClick={handleRetry}>
        <div className={classNames(styles.container, {
          [styles.containerLight]: !isDarkMode()
        })}>
          <Header title={ALL_BREAKOUTS} subTitle={ALL_BREAKOUTS_SUBTITLE} handleBackPress={handleBackPress} />

          <SwipeableTabs
            isEmpty={data.length === 0}
            subTab={<Filters filterClick={onFilterClick} activeTab={activeSubTab} setActiveTab={setActiveSubTab} />}
            className={styles.contentWrapper}
            activeTab={activeTab}
            setActiveTab={setActiveTab}
            content={HOME_BREAKOUT_TABS.map((tab) => ({
              id: tab.id,
              children: <Content />,
            }))}
          />
          {hasMore ? paginationLoader() : null}
        </div>

        <DrawerContent isOpen={isOpen} description={breakoutDetails?.longDesc} title={breakoutDetails?.breakoutName} onClose={onClose} />
      </HeaderLayout>

    </>
  )
}


export default baseComponent(CompanyBreakout);
