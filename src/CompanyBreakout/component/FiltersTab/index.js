import classNames from 'classnames';
import React from 'react';
import { FILTERS } from '../../utils/Constants';
import { FILTER_LIST } from '../../utils/enums';
import styles from './index.scss';



function FiltersTab({ filterClick, activeTab, setActiveTab }) {

  const onFilterClick = (item, index) => {
    setActiveTab(index);
    filterClick(item)
  }

  const Filter = ({ item, index }) => {
    return (
      <div
        className={classNames(styles.filter, {
          [styles.activeTab]: activeTab === index,
        })}
        onClick={index !== undefined ? () => onFilterClick(item, index) : () => { }}
      >
        <div className={styles.filterTitle}>{item.name}</div>
        {item.icon ? <img className={styles.filterIcon} src={FILTERS} /> : null}
      </div>
    );
  };

  return (
    <>
      <div className={styles.container}>
        {/* <div className={styles.info}>
          {FILTER_INFO}
        </div> */}
        <div className={styles.filtersContainer}>
          {FILTER_LIST.map((item, index) => {
            return <Filter item={item} key={item.name} index={index} />;
          })}
        </div>
      </div>
      {/* <Drawer
        isOpen={isOpen}
        showCross={false}
        className={styles.drawer}
        customHeadingColor={styles.drawerTitle}
        align={ALIGNMENTS.LEFT}
        onClose={onClose}
      >
        <Filters />
      </Drawer> */}
    </>
  );
}

export default FiltersTab;
