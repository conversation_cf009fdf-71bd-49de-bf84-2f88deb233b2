import React from 'react';

import classNames from 'classnames';
import { isDarkMode } from '../../../utils/commonUtils';
import styles from './index.scss';

function ShimmerLoading() {
  return (
    <div className={classNames(styles.container, {
      [styles.containerLight]: !isDarkMode()
    })} >
      <div
        className={classNames(styles.companyLogo, styles.shimmerAnimation)}
      />
      <div className={styles.stockNameContainer}>
        <div
          className={classNames(styles.stockName, styles.shimmerAnimation)}
        />
        <div className={classNames(styles.exchange, styles.shimmerAnimation)} />
      </div>
      <div className={styles.priceContainer}>
        <div className={classNames(styles.price, styles.shimmerAnimation)} />
      </div>
    </div>
  );
}

export default ShimmerLoading;
