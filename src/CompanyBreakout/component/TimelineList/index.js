import classNames from 'classnames';
import React from 'react';
import { formatTimestamp } from '../../utils/utilFn';
import Card from './_partials/Card';
import Header from './_partials/Header';
import styles from './index.scss';

function TimelineList({ data, onInfoClick, breakoutJSON }) {

  return (
    <>
      {Object.keys(data || {})?.map((item, index) => (
        <div className={styles.container}>
          <Header date={formatTimestamp(item)} />
          <div className={classNames(styles.cardContainer, {
            [styles.noBorder]: index === Object.keys(data || {}).length - 1
          })}>
            <div className={styles.cardWrapper}>
              {data[item]?.map((content) => (
                <div key={content.bId}>
                <Card
                  className={styles.card}
                  title={breakoutJSON.find(breakout => breakout.breakoutId === content?.bId)?.breakoutName || ''}
                  description={breakoutJSON.find(breakout => breakout.breakoutId === content?.bId)?.shortDesc || ''}
                  onInfoClick={() => onInfoClick(content.bId)}
                  bTrend={content.bTrend}
                />
                </div>
              ))}
            </div>
          </div>
        </div>
      ))}
    </>
  );
}

export default TimelineList;
