import React from 'react';
import cx from 'classnames';
import { isEmpty } from 'lodash';

import styles from './index.scss';

const Tab = ({ children, customHeader, tabs, activeTab, tabOnClick, isScrolled }) => {
  const tabClickHandler = tab => {
    if (tab === activeTab) return;
    tabOnClick(tab);
  };

  return (
    <>
      <div className={cx(styles.tabContainer, {
        [styles.isScrolled]: isScrolled
      })}>
        {!isEmpty(customHeader) ? (
          customHeader
        ) : (
          <>
            {tabs.map((tab, index) => (
              <div
                className={cx(styles.tab, {
                  [styles.activeTabClass]: activeTab === index,
                })}
                key={tab.id}
                onClick={() => tabClickHandler(index)}
              >
                <div className={styles.tabContent}>
                  {tab.icon ? (
                    <img src={tab.icon} className={styles.icon} alt="" />
                  ) : null}
                  <span>{tab.name}</span>
                </div>
              </div>
            ))}
          </>
        )}
      </div>
      {children}
    </>
  );
};

export default Tab;
