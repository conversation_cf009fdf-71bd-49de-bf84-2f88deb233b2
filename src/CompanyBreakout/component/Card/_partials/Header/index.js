import React from 'react';
import styles from './index.scss';
import Icon from '../../../../../components/Icon';
import PriceBroadcast from '../PriceBroadcast';
import { useStockFeed } from '../../../../../hooks/equities';

function Header({ id, name, exchange, securityId, segment }) {
  const { ltp, pClose, percentageChange } = useStockFeed({
    exchange,
    securityId,
    segment,
  });


  return (
    <div className={styles.container}>
      <Icon name={String(id)} width={32} className={styles.companyIcon} companyName={name} />
      <div className={styles.titleContainer}>
        <div className={styles.title}>
          {name}
        </div>
        <div className={styles.exchange}>{exchange}</div>
      </div>
      <PriceBroadcast ltp={ltp} pClose={pClose} percentageChange={percentageChange} />
    </div>
  )
}

export default Header