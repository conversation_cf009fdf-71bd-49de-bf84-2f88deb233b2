import React from 'react';
import { formatTimestamp } from '../../../../utils/utilFn';
import Card from '../../../TimelineList/_partials/Card';
import styles from './index.scss';

function Body({ bTime, title, description, onInfoClick, bId, ...item }) {
  return (
    <div className={styles.container}>
      <Card title={title} description={description} {...item} onInfoClick={() => onInfoClick(bId)} />
      <div className={styles.footer}>
        <div className={styles.date}>{formatTimestamp(bTime)}</div>
        {/* <div className={styles.breakouts}>+{bCount} {BREAKOUTS}</div> */}
      </div>
    </div>
  )
}

export default Body;