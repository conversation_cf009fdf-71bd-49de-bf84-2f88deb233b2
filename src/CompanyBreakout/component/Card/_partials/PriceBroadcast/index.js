import classNames from 'classnames';
import React from 'react';
import Shimmer from '../../../../../components/Shimmer/Shimmer';
import { roundValue } from '../../../../../utils/commonUtils';
import styles from './index.scss';

const PriceBroadcast = ({ ltp, pClose, percentageChange }) => {
  const renderGetChange = () => {
    const percentChange = roundValue(Math.abs(percentageChange));
    const priceChange = roundValue(ltp - pClose);
    const isProfit = priceChange > 0;
    const priceChangeStyle = isProfit ? styles.profit : styles.loss;

    return (
      <span className={classNames(priceChangeStyle, styles.change)}>{`${isProfit ? '+' : ' '
        }${priceChange} (${percentChange}%)`}</span>
    );
  };

  return (
    <div className={styles.container}>
      {ltp === undefined ? (
        <Shimmer height="15px" width="70px" />
      ) : (
        <>
            <div className={styles.ltp}>{roundValue(ltp)}</div>
          {renderGetChange()}
        </>
      )}
    </div>
  );
};

export default PriceBroadcast;
