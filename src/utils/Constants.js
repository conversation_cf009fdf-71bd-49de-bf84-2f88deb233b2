export const SW = true;
export const RUPEE_SYMBOL = '₹';

export const CLIENT_TYPE = {
  H5: 'H5',
  mWeb: 'mWeb',
};

export const COPY_TO_CLIPBOARD_TEXT = 'Copied to clipboard!';

export const COOKIES = {
  SSO_TOKEN: 'x-sso-token',
  TWOFA_TOKEN: 'x-2fa-token',
  USER_AGENT: 'x-user-agent',
  UID: 'x-uid',
};

export const SEO = {
  BUYBACK_DASHBOARD: {
    TITLE: 'Buyback Dashboard',
    DESC: 'View current, past and upcoming Buyback',
  },
  APPLY_FOR_BUYBACK: {
    TITLE: 'Apply for Buyback',
    DESC: 'Apply for Buyback',
  },
  BUYBACK_DETAILS: {
    TITLE: 'Buyback Details',
    DESC: 'Buyback Details',
  },
  BUYBACK_ORDER_DETAILS: {
    TITLE: 'Buyback Order Details',
    DESC: 'Buyback Order Details',
  },
  BUYBACK_ORDER_LIST: {
    TITLE: 'Buyback Order List',
    DESC: 'Buyback Order List',
  },
  MTF_ONBOARDING: {
    TITLE: 'MTF Onboarding',
    DESC: 'MTF Onboarding',
  },
  MTF_SCRIPS: {
    TITLE: 'MTF Scrips',
    DESC: 'MTF Scrips',
  },
  MTF_PLEDGE: {
    TITLE: 'MTF Pledge',
    DESC: 'MTF Pledge',
  },
  MTF_CALCULATOR: {
    TITLE: 'MTF Calculator',
    DESC: 'Calculate MTF margins and requirements',
  },
  COMPANY_BREAKOUT_VIEW_ALL: {
    TITLE: 'Company Breakout List',
    DESC: 'Company Breakout List',
  },
  COMPANY_BREAKOUT: {
    TITLE: 'Company Breakout',
    DESC: 'Company Breakout',
  },
  DEMAT_HOLDINGS: {
    TITLE: 'Demat Holdings',
    DESC: 'Demat Holdings',
  },
};

export const FAQ_TYPE = {
  BULLETS: 'BULLETS',
  ICONS: 'ICONS',
  MULTI_BULLETS: 'MULTI_BULLETS',
};

export const ACTIONS = {
  SHOW_LOADER: 'SHOW_LOADER',
  SHOW_ROOT_ERROR: 'SHOW_ROOT_ERROR',
  SHOW_ROOT_ERROR_SCREEN: 'SHOW_ROOT_ERROR_SCREEN',
  SET_CONNECTION_STATUS: 'SET_CONNECTION_STATUS',
  SHOW_PAGE_ERROR: 'SHOW_PAGE_ERROR',
  SAVE_USER_SESSION: 'SAVE_USER_SESSION',
  OPEN_USER_LOGIN_POPUP: 'OPEN_USER_LOGIN_POPUP',
  SET_H5_NATIVE_DEEP_LINK_DATA: 'SET_H5_NATIVE_DEEP_LINK_DATA',
  SET_APP_STATUS: 'SET_APP_STATUS',
  INFO_MESSAGE: 'INFO_MESSAGE',
  SET_WEB_UTILITIES: 'SET_WEB_UTILITIES',
};

export const PULSE_EVENTS = {
  event: 'custom_event',
  verticalName: 'pm_mini_app',
  actions: {},
};
export const NO_INTERNET = {
  description:
    'Looks like you are not connected to the internet. Make sure Wi-Fi is on and then try again',
};

export const IPO_SCREEN_NAMES = {
  IPO_CARD: 'ipo-card',
  OPEN_UPCOMING_IPOS: 'view-upcoming-open-ipo',
  CLOSED_IPOS: 'view-closed-ipo',
  APPLY_IPO: 'apply-ipo',
  MODIFY_IPO: 'application-modify-ipo',
  APPLICATION_STATUS: 'application-status',
  ALL_APPLICATIONS: 'ipo-applications',
  VIEW_IPO_APPLICATION: 'view_ipo_application',
  VIEW_IPO_VIDEOS: 'view-ipo-video',
  COMPLETE_KYC: 'complete-kyc',
  VIEW_DETAILED_REPORT: 'view-detailed-report',
  DOWNLOAD_PM_APP: 'download-paytm-money',
};

export const SWIPE_MODE = {
  SWIPE_LEFT: 0,
  SWIPE_RIGHT: 1,
  SWIPE_TOP: 2,
  SWIPE_BOTTOM: 3,
};

export const EMPTY_SCREEN = {
  NO_INTERNET: {
    MESSAGE: 'No internet access',
    SUB_MESSAGE: 'Please check your network connection and try again',
    RETRY: 'RETRY',
  },
};

export const CONNECTION_MODE = {
  ONLINE: 'online',
  OFFLINE: 'offline',
};

export const GENERIC_ERROR_MSG = 'Oops, something went wrong.';
export const SESSION_EXPIRED_MSG = 'Your session has expired!';

export const NO_DATA_FOUND = 'No data found. Please try again';

export const SHOW_HELP_DRAWER_COUNTER = 3;

export const CACHE_NOT_TO_REFRESH_ON_UPDATE = ['be-images', 'static-images'];

export const ENABLE_SCREENSHOT_MODE = {
  ALLOWED: 'allowed',
  RESTRICTED: 'restricted',
  SECURED: 'secured',
};

export const LOCAL_STORAGE_KEYS = {
  TRADETRON_PML_COHORT: 'tradetron_pml_cohort',
};
