/* eslint-disable no-unreachable */
import get from 'lodash/get';
// import queryString from 'query-string';

import {
  log,
  isPaytmMoney,
  // isDarkMode,
  isIosBuild,
  getOrigin,
  isAppVersionGreaterThan940,
  getQueryParams,
  generateQueryParamsString,
  getQueryParamsFromString,
  getCookieValue,
  isPaytm,
  isDarkMode,
} from './commonUtils';
import DeviceInfoProvider from './Providers/DeviceInfoProvider';
import {
  getMobileOperatingSystem,
  // setIsConsentOverlayShown,
  setH5NativeDeepLink,
  isPhoenixContainer,
  isH5Container,
  navigateTo,
  getInitialQueryParams,
} from '../services/coreUtil';
// import { IPO_API_URL } from '../ipo/config/urlConfig';
import { setH5NativeDeepLinkData } from '../actions/genericActions';
import {
  COPY_TO_CLIPBOARD_TEXT,
  COOKIES,
  SESSION_EXPIRED_MSG,
} from './Constants';
import { EQ_AUTH_URL, LOGIN_URL } from '../config/urlConfig';
import history from '../history';
import URL from '../routes/config/urlConfig';
import { sendErrorToBackend } from '../actions/runtime';

export const getBridge = () =>
  isPhoenixContainer() ? window.JSBridge : window.AlipayJSBridge;

function processOnBridgeReady() {
  log('##### processOnBridgeReady Removed.....');
  document.removeEventListener(
    isPhoenixContainer() ? 'JSBridgeReady' : 'AlipayJSBridgeReady',
    processOnBridgeReady,
  );
}

function bridgePromise(bridgeName) {
  return new Promise((resolve, reject) => {
    const bridge = getBridge();
    if (bridge) {
      bridge.call(bridgeName, result => {
        resolve(result);
      });
    } else {
      reject();
    }
  });
}

function bridgeObserver(callackFn) {
  const bridgeName = getBridge();
  if (typeof bridgeName === 'undefined') {
    document.addEventListener(
      isPhoenixContainer() ? 'JSBridgeReady' : 'AlipayJSBridgeReady',
      callackFn,
    );
  } else if (callackFn) {
    callackFn();
  }
}

// const checkForStatusHeight = data => {
//   let appVersion = get(data, 'appVersionName');
//   const device_type = get(data, 'platform');
//   appVersion = appVersion || '8.4.0';
//   const base =
//     DeviceInfoProvider.getInfo('device_type') === 'ios' ? '81.4.3' : '18.10.0';
//   if (isSupportedVersion(appVersion, base, device_type)) {
//     window.statusBar = false;
//     return false;
//   }
//   window.statusBar = true;
//   return true;
// };

// New set of bridges

export const setLandspaceMode = () => {
  getBridge().call('setLandscape', {}, e => {
    log(e);
  });
};

export const setPotraitMode = () => {
  getBridge().call('setPortrait', {}, e => {
    log(e);
  });
};

export function getAuthTokenFromBridge(callback) {
  log('getAuthTokenFromBridge called!!');
  getBridge().call('paytmFetchAuthToken', result => {
    log(
      `paytmFetchAuthToken Bridge is called for Mini app and result is :::${JSON.stringify(
        result,
      )}`,
    );
    callback(result);
  });
}

export function getCustomerIdFromBridge(callback) {
  log('getCustomerIdFromBridge called!!');
  getBridge().call('paytmFetchCustomerId', result => {
    log(
      `paytmFetchCustomerId Bridge is called for Mini app and result is :::${JSON.stringify(
        result,
      )}`,
    );
    callback(result);
  });
}

export function getDeviceNameFromBridge(callback) {
  log('getDeviceNameFromBridge called!');
  getBridge().call('paytmDeviceName', result => {
    callback(result);
  });
}

export function getDeviceLocaleFromBridge(callback) {
  getBridge().call('paytmDeviceLocale', result => {
    callback(result);
  });
}

export function getNetworkTypeFromBridge(callback) {
  getBridge().call('paytmNetworkType', result => {
    callback(result);
  });
}

export function getDeviceImeiFromBridge(callback) {
  getBridge().call('paytmDeviceImei', result => {
    log(`imei bridge called....${JSON.stringify(result)}`);
    callback(result);
  });
}

export function getOsVersionFromBridge(callback) {
  log('getOsVersionFromBridge called!!');
  getBridge().call('paytmOsVersion', result => {
    callback(result);
  });
}

export function getIsPlaystoreInstallFromBridge(callback) {
  getBridge().call('paytmIsPlaystoreInstall', result => {
    callback(result);
  });
}

export async function getWalletTokenFromBridge() {
  const bridgeName = getBridge();
  return new Promise(resolve => {
    if (typeof bridgeName !== 'undefined') {
      bridgeName.call('paytmFetchWalletToken', {}, result => {
        resolve(result);
      });
    } else {
      resolve('');
    }
  });
}

// export const getTitleAndStatusBarHeight = cb => {
//   const bridgeName = getBridge();
//   log('status bar bridge called....');
//   if (bridgeName) {
//     bridgeName.call('getTitleAndStatusBarHeight', {}, e => {
//       log(`getTitleAndStatusBarHeight........${JSON.stringify(e)}`);
//       if (isPaytmMoney() || isIosBuild()) {
//         localStorage.removeItem('stsBarHt');
//       } else if (e && e.statusBarHeight) {
//         log('status bar height setting 111');
//         const query = queryString.parse(window.location?.search);
//         log(`query String check here...${JSON.stringify(query)}`);
//         if (query && get(query, 'stsBarHt', false)) {
//           log(`status bar height setting 22${e.statusBarHeight}`);
//           localStorage.setItem('stsBarHt', e.statusBarHeight);
//         } else {
//           log('no sts value in query.. so dont add extra space here...');
//           localStorage.removeItem('stsBarHt');
//         }
//       } else {
//         log('status bar height setting 33 ');
//         localStorage.removeItem('stsBarHt');
//       }
//       if (cb) {
//         cb(e);
//       }
//     });
//   } else {
//     log('getTitleAndStatusBarHeight. Error.......');
//     cb(false);
//   }
// };

export function getPaytmAppInfoFromBridge(callback) {
  log('getPaytmAppInfoFromBridge:');
  getBridge().call('paytmGetAppInfo', result => {
    callback(result);
  });
}

export function setLoginAuthValues() {
  try {
    const ssoToken = getCookieValue(COOKIES.SSO_TOKEN);
    const userAgent = getCookieValue(COOKIES.USER_AGENT);

    let userId;
    if (userAgent) {
      userId = JSON.parse(userAgent).user_id;
    }

    if (ssoToken && userAgent && userId) {
      DeviceInfoProvider.setInfo('sso_token', ssoToken);

      const isLogin = true;
      localStorage.setItem('login', isLogin);
      const event = new Event('customLoginEvent');
      window.dispatchEvent(event);
      DeviceInfoProvider.setInfo('isLogin', isLogin);

      DeviceInfoProvider.setInfo('userId', userId);
    } else {
      // eslint-disable-next-line no-use-before-define
      paytmLogin();
    }
  } catch (error) {
    // eslint-disable-next-line no-use-before-define
    paytmLogin();
  }
}

const paytmFetchAuthTokenCallback = (result, reject) => {
  log(`getAuthTokenFromBridge called.....${JSON.stringify(result)}`);
  if (result && result.data) {
    log('Authorization.....', result.authorization, 'sso_token: ', result.data);

    // eslint-disable-next-line no-undef
    if (__ENV__ !== 'staging') {
      DeviceInfoProvider.setInfo('sso_token', result.data);
    }

    let isLogin = true;

    const token = result.data;
    if (token === '' || token === 'null' || token === null) {
      isLogin = false;
      if (reject) {
        reject('No token found');
      }
    }
    localStorage.setItem('login', isLogin);
    const event = new Event('customLoginEvent');
    window.dispatchEvent(event);
    DeviceInfoProvider.setInfo('isLogin', isLogin);
  }
};

const paytmFetchCustomerIdCallback = result => {
  // eslint-disable-next-line no-undef
  if (result && result.data && __ENV__ !== 'staging') {
    DeviceInfoProvider.setInfo('userId', result.data);
  }
};

const paytmDeviceNameCallback = result => {
  if (result && result.data) {
    DeviceInfoProvider.setInfo('deviceName', result.data);
  }
};

const paytmOsVersionCallback = result => {
  if (result && result.data) {
    DeviceInfoProvider.setInfo('osVersion', result.data);
  }
};
export function getDeviceManufacturerCallback(result) {
  if (result && result.data) {
    DeviceInfoProvider.setInfo('deviceManufacturer', result.data);
  }
}

const paytmGetAppInfoCallback = result => {
  if (result && result.data) {
    DeviceInfoProvider.setInfo(
      'appVersionName',
      result?.data?.appVersionName || result?.appVersionName,
    );
    DeviceInfoProvider.setInfo('h5Version', result.data.h5Version);
    DeviceInfoProvider.setInfo(
      'deviceId',
      result.data.app_info_pt?.deviceIdentifier,
    );
    DeviceInfoProvider.setInfo('client', result.data.app_info_pt?.client);
  }
};

const pmFetch2faTokenCallback = result => {
  if (result && result.data) {
    DeviceInfoProvider.setInfo('x-2fa-token', result.data['x-2fa-token']);
    DeviceInfoProvider.setInfo('x-2fa-token-expiry', result.data.expiryTime);
  }
};

const INITIAL_BRIDGE_CALLS = {
  // paytmFetchAuthToken: paytmFetchAuthTokenCallback,
  // paytmFetchCustomerId: paytmFetchCustomerIdCallback,
  paytmDeviceName: paytmDeviceNameCallback,
  paytmOsVersion: paytmOsVersionCallback,
  paytmGetAppInfo: paytmGetAppInfoCallback,
  paytmDeviceManufacturer: getDeviceManufacturerCallback,
  pmFetch2faToken: pmFetch2faTokenCallback,
};

export const initDeviceParams = () =>
  new Promise((resolve, reject) => {
    log('init Params called.....', resolve);

    const initialBridgeCalls = Object.keys(INITIAL_BRIDGE_CALLS);

    const initialBridgeCallsPromises = initialBridgeCalls.map(bridgeName =>
      bridgePromise(bridgeName),
    );

    Promise.all(initialBridgeCallsPromises)
      .then(results => {
        results.forEach((result, index) =>
          INITIAL_BRIDGE_CALLS[initialBridgeCalls[index]](result, reject),
        );
        // eslint-disable-next-line no-undef
        // if (__ENV__ === 'staging') {
        //   // eslint-disable-next-line no-alert
        //   const ssoToken = prompt('Please enter custom sso token if you want');
        //   if (ssoToken) {
        //     DeviceInfoProvider.setInfo('sso_token', ssoToken);
        //   }
        //
        //   // eslint-disable-next-line no-alert
        //   const userID = prompt('Please enter custom userId if you want');
        //   if (userID) {
        //     DeviceInfoProvider.setInfo('userId', userID);
        //   }
        // }
        resolve();
      })
      .catch(err => {
        log(err);
      });
  });

const INITIAL_AUTH_BRIDGE_CALLS = {
  paytmFetchAuthToken: paytmFetchAuthTokenCallback,
  paytmFetchCustomerId: paytmFetchCustomerIdCallback,
};

export function initRequiredParams() {
  return new Promise((resolve, reject) => {
    {
      log('init Params called.....', resolve);
      const initialAuthBridgeCalls = Object.keys(INITIAL_AUTH_BRIDGE_CALLS);

      const initialAuthBridgeCallsPromises = initialAuthBridgeCalls.map(
        bridgeName => bridgePromise(bridgeName),
      );

      if (!isPaytm()) {
        Promise.all(initialAuthBridgeCallsPromises)
          .then(results => {
            results.forEach((result, index) =>
              INITIAL_AUTH_BRIDGE_CALLS[initialAuthBridgeCalls[index]](
                result,
                reject,
              ),
            );

            // eslint-disable-next-line no-undef
            if (__ENV__ === 'staging') {
              // eslint-disable-next-line no-alert
              const ssoToken = prompt(
                'Please enter custom sso token if you want',
              );
              if (ssoToken) {
                DeviceInfoProvider.setInfo('sso_token', ssoToken);
              }

              // eslint-disable-next-line no-alert
              const userID = prompt('Please enter custom userId if you want');
              if (userID) {
                DeviceInfoProvider.setInfo('userId', userID);
              }
            }

            initDeviceParams()
              .then(() => {
                resolve();
              })
              .catch(err => {
                log(err);
              });
          })
          .catch(err => {
            log(err);
          });
      } else {
        setLoginAuthValues();
        initDeviceParams()
          .then(() => {
            resolve();
          })
          .catch(err => {
            log(err);
          });
      }
    }
  });
}

function removeH5Loader() {
  const bridgeName = getBridge();
  if (bridgeName) {
    bridgeName.call(
      'paytmNavigateTo',
      {
        navigateTo: 'removeLoader',
        data: {},
      },
      () => {},
    );
  }
}

function initH5NativeDeepLinkUrl(deepLink) {
  log('##initH5NativeDeepLinkUrl');
  setH5NativeDeepLink(deepLink); // set in DeviceInfoProvider(since window._dispatch is not available on initial load)
  setH5NativeDeepLinkData(deepLink); // set deeplink in context
}

export const initDeepLinkData = callback => {
  getBridge().call(
    'getStartupParams',
    {
      keys: ['deeplinkData'],
    },
    result => {
      log('initDeepLinkData result..MINI APP..', result);
      log('deeplinkData: ', result?.deeplinkData);
      if (result?.deeplinkData) {
        initH5NativeDeepLinkUrl(result.deeplinkData);
      }
      callback(result || {});
    },
  );
};

export const enableScreenShot = (
  isEnable = true,
  screenshotMode = 'allowed',
) => {
  const bridgeName = getBridge();
  if (bridgeName) {
    bridgeName.call(
      'PaytmSetScreenshotStatus',
      {
        isEnable,
        screenshotMode,
      },
      result => {
        log('PaytmSetScreenshotStatus', result);
      },
      { version: 2 },
    );
  }
};

export const shareScreenshot = customMsg => {
  const bridgeName = getBridge();
  if (bridgeName) {
    bridgeName.call(
      'paytmShareScreenshot',
      {
        message: customMsg,
      },
      result => {
        log(result);
      },
    );
  }
};

export function initBridges() {
  log('##### init bridge called.....');
  return new Promise(resolve => {
    log('##### init bridge called.....resolved');
    bridgeObserver(() => {
      initDeepLinkData(() => {
        log('initDeepLinkData callback');
      });
      initRequiredParams()
        .then(() => {
          removeH5Loader();
          resolve();
        })
        .catch(err => {
          log('initDeepLinkData error', err);
        });

      enableScreenShot();
      // getTitleAndStatusBarHeight();
      processOnBridgeReady();
    });
  });
}

/* set page tile bar  */
export function setTitleBar(pageTitle) {
  if (!pageTitle) return;
  const bridgeName = getBridge();
  const upperCase = pageTitle.charAt(0).toUpperCase() + pageTitle.slice(1);
  if (typeof bridgeName !== 'undefined') {
    bridgeName.call('setTitle', { title: upperCase });
  }
}

export function openPaymentpage(data, totalPrice, extraParams, callback) {
  const bridgeName = getBridge();
  const {
    isBuy,
    vertical_id,
    auto_add_money,
    paymentType,
    pgUrlToHit,
    nativeSdkEnabled,
  } = extraParams;
  const postData = {
    data: JSON.stringify(data),
    totalPrice,
    vertical_id, // dynamic
    'intent-type-is-buy-flow': isBuy,
    Tab: isBuy ? 'buy' : 'sell',
    auto_add_money,
    paymentType,
    pgUrlToHit,
    nativeSdkEnabled,
  };
  if (bridgeName) {
    bridgeName.call(
      'paytmNavigateTo',
      {
        navigateTo: 'goldPaymentGatewayPage',
        data: postData,
      },
      result => {
        if (callback) {
          callback(result);
        }
      },
    );
  }
}

export function navigatePaytmHome() {
  const bridgeName = getBridge();
  if (typeof bridgeName === 'undefined') {
    return;
  }

  bridgeName.call('popWindow');
}

export function requestPermission(type, callback) {
  const bridgeName = getBridge();
  if (typeof bridgeName !== 'undefined') {
    bridgeName.call(
      'paytmRequestPermission',
      {
        permission: type,
      },
      result => {
        callback(result);
      },
    );
  }
}

export function takePicture(cb) {
  const bridgeName = getBridge();
  if (typeof bridgeName !== 'undefined') {
    bridgeName.call(
      'paytmCaptureImageFromCamera',
      {
        requestCode: 124,
        title: 'My Title',
      },
      result => {
        cb(result);
      },
    );
  }
}

export function openGallery(cb) {
  const bridgeName = getBridge();
  if (typeof bridgeName !== 'undefined') {
    bridgeName.call(
      'paytmOpenGallery',
      {
        requestCode: 123,
        type: 'image',
        title: 'My Title',
      },
      result => {
        cb(result);
      },
    );
  }
}

// export async function getSignature(url, cb) {
//   return new Promise(resolve => {
//     if (typeof getBridge() !== 'undefined') {
//       getBridge().call(
//         'paytmFetchSignature',
//         {
//           url,
//         },
//         result => {
//           // return result
//           resolve(result);
//           // cb(result);
//         },
//       );
//     }
//   });
// }

export async function verifyPasscode(passcode) {
  const bridgeName = getBridge();
  return new Promise(resolve => {
    if (typeof bridgeName !== 'undefined') {
      bridgeName.call(
        'paytmVerifyPasscode',
        {
          passcode,
        },
        result => {
          resolve(result);
        },
      );
    }
  });
}

// Login

export const login = () =>
  new Promise((resolve, reject) => {
    log('login() called!!');
    if (typeof getBridge() === 'undefined') {
      // log('type of alipaybridge undefined!!');
      reject(new Error('type of alipaybridge undefined!!'));
    }

    // DeviceInfoProvider.getInfo('device_type') === 'android' && isPaytmMoney()
    //   ? (document.getElementById('loaderCssID').style.display = 'flex')
    //   : '';
    if (
      getOrigin() === 'PAYTM' &&
      isAppVersionGreaterThan940(DeviceInfoProvider.getInfo('appVersionName'))
    ) {
      window.JSBridge.call('paytmAuthHandler', e => {
        if (e && e?.data && e.data?.success && e.data?.authToken) {
          DeviceInfoProvider.setInfo('sso_token', e.data.authToken);
          initRequiredParams(resolve, reject);
        }
      });
    } else {
      getBridge().call(
        'paytmNavigateTo',
        {
          navigateTo: 'paytmLogin',
          expectingResultBack: !0,
          data: {},
        },
        e => {
          localStorage.setItem('isH5', 1);
          log('event data in login bridge: ', e);
          if (e && e.data && e.data.data) {
            DeviceInfoProvider.setInfo('sso_token', e.data.data);
          } else if (e && e.data) {
            log('e.data is available: with sso token', e.data);
            DeviceInfoProvider.setInfo('sso_token', e.data);
          }
          if (e && e.data) {
            DeviceInfoProvider.setInfo('isLogin', true);
            localStorage.setItem('login', true);
          }
          if (e?.data?.data || e?.data) {
            log('e.data. is available: with sso token', e?.data);
          } else {
            log('set sso token to empty ', e);
            DeviceInfoProvider.setInfo('sso_token', '');
          }
          initRequiredParams(resolve, reject);
        },
      );
    }
  });

export const exitApp = () => {
  getBridge().call('popWindow');
};

export function getData(callback, keys) {
  getBridge().call(
    'paytmGetData',
    {
      keys,
      fileName: 'SHARED_PREF_FILE/app_pref',
    },
    result => {
      callback(result);
    },
  );
}

export function pushWindow(url, callback) {
  getBridge().call(
    'paytmPushWindow',
    {
      appId: '8cc7a6ac4e344a17a7b3ff069929b557',
      url,
      param: {
        pullRefresh: 'NO',
        canPullDown: 'NO',
        showTitleBar: true,
        readTitle: false,
        showLoading: 'NO',
        showProgress: 'YES',
      },
    },
    result => {
      log('bridge result:', result);
      if (callback && result?.data?.success) {
        callback(result);
      }
    },
  );
}

export const openContacts = cb => {
  getBridge().call(
    'paytmNavigateTo',
    {
      navigateTo: 'paytmPickContact',
      data: {},
    },
    result => {
      if (result.data && result.data.data) {
        cb(result.data.data);
      } else {
        cb(result.data);
      }
    },
  );
};

export const isH5 = () => {
  return;
  log('Is H5 Check Called....');
  log(`Is H5 Check Called....OS Checks....${getMobileOperatingSystem()}`);
  try {
    if (localStorage.getItem('isH5') === 1) {
      return true;
    }
    if (isH5Container() || isPhoenixContainer()) {
      // storatge is set from client.js
      localStorage.setItem('isH5', 1);
      return true;
    }
    return false;
  } catch (e) {
    log(e);
    return true;
  }
};

export const isBridge = () => isH5();

export const openNewPage = (url, callback) => {
  try {
    if (isBridge()) {
      pushWindow(url, callback);
    } else {
      window.open(url);
    }
  } catch (e) {
    log(e);
  }
};

export const openGoldDeeplink = (url, data) => {
  const appBridge = getBridge();
  if (appBridge)
    appBridge.call(
      'paytmNavigateTo',
      {
        navigateTo: 'openGoldDeeplink',
        clearBackStack: false, // to clear whole back stack of application
        finishThis: false, // to finish this activity
        data: data || { data: url },
      },
      result => {
        log(`openDeepLink result....${JSON.stringify(result)}`);
      },
    );
};

export const openDeepLink = (url, data) => {
  const appBridge = getBridge();
  if (isPhoenixContainer() && getOrigin() === 'PAYTM') {
    appBridge.call(
      'paytmOpenDeeplink',
      {
        deeplink: url,
      },
      result => {
        log(`openDeepLink result....${JSON.stringify(result)}`);
      },
    );
  } else {
    appBridge.call(
      'paytmNavigateTo',
      {
        navigateTo: 'openGoldDeeplink',
        clearBackStack: false, // to clear whole back stack of application
        finishThis: false, // to finish this activity
        data: data || { data: url },
      },
      result => {
        log(`openDeepLink result....${JSON.stringify(result)}`);
      },
    );
  }
};

export function openInBrowser(url) {
  const bridgeName = getBridge();
  if (typeof bridgeName === 'undefined') {
    return;
  }
  bridgeName.call('openInBrowser', {
    url,
  });
}

export const handleDeeplink = (url, data) => {
  if (url.includes('paytmmp://')) {
    openDeepLink(url, data);
  } else {
    openInBrowser(url);
  }
};

export const goToPG = (data, extraParams, callback) => {
  openPaymentpage(data, data.total_price, extraParams, callback);
};

export const paytmLogin = (withRefresh = true) => {
  try {
    const data = {
      isBridge: isBridge(),
      isPaytm: isPaytm(),
      origin: getOrigin(),
      page: window?.location?.href,
    };
    console.log('paytmLogin try ::', data);
    sendErrorToBackend({
      data,
      level: 'info',
      key: 'paytm-login',
    });
  } catch (e) {
    console.log('paytmLogin catchError ::', e);
  }
  const queryParams = {
    ...getQueryParamsFromString(getInitialQueryParams()),
    ...getQueryParams(),
  };

  const redirectionUrl = `${window.location.origin}${
    window.location.pathname
  }${generateQueryParamsString(queryParams)}`;

  log('redirectionUrl', redirectionUrl);

  let url = LOGIN_URL.replace(
    '{redirectURL}',
    encodeURIComponent(redirectionUrl),
  );

  if (withRefresh) {
    url = `${url}&refresh=true`;
  }

  window.location.replace(url);
  return true;
};

export function doLogin() {
  try {
    const data = {
      isBridge: isBridge(),
      isPaytm: isPaytm(),
      origin: getOrigin(),
      page: window?.location?.href,
    };
    console.log('doLogin try ::', data);
    sendErrorToBackend({
      data,
      level: 'info',
      key: 'do-login',
    });
  } catch (e) {
    console.log('doLogin catchError ::', e);
  }
  log('login fnc called!');
  if (isBridge() && !isPaytm()) {
    console.log('isBridge: true && !isPaytm(): true');
    return login()
      .then(res => {
        log('login successed', res);
        // setIsConsentOverlayShown('false');
        return true;
      })
      .catch(err => {
        log('login failed', err);
        return false;
      });
  } else if (isBridge()) {
    // eslint-disable-next-line no-use-before-define
    deleteCookie(COOKIES.SSO_TOKEN);
    // eslint-disable-next-line no-use-before-define
    deleteCookie(COOKIES.USER_AGENT);

    paytmLogin(true);
    return true;
  }

  log('is bridge false: do login');
  const url = EQ_AUTH_URL.LOGIN_URL.replace(
    '{redirectURL}',
    encodeURIComponent(window.location.href),
  );

  window.location.replace(url);
  return true;
}

export const callTextShare = (url, title, subject) => {
  if (isBridge()) {
    getBridge().call(
      'paytmShareText',
      {
        title,
        subject,
        text: url,
      },
      result => {
        log(JSON.stringify(result));
      },
    );
  } else {
    window.open(url);
  }
};

export const getSSOToken = callback => {
  getBridge().call(
    'getStartupParams',
    {
      keys: ['ssoToken'], // keys array
    },
    result => {
      //  Logger(`getSSOToken result....${JSON.stringify(result)}`);

      if (result && result.ssoToken) {
        callback(result || {});
      } else {
        getBridge().call(
          'paytmFetchValuesForKeys',
          {
            keys: ['ssoToken'], // keys array
          },
          innerResult => {
            callback((innerResult && innerResult.data) || {});

            // result-> {"data":{"name":"XYZ","ssoToken":"sdljkcjk8od787iumcnbui"}}
          },
        );
      }
      // result-> {"name":"XYZ","ssoToken":"sdljkcjk8od787iumcnbui"}
    },
  );
};

export function getDeviceLocation() {
  const promise = new Promise(resolve => {
    if (isBridge()) {
      getBridge().call('paytmGetLocation', {}, result => {
        resolve(result);
      });
    } else if (navigator.geolocation) {
      function showPosition(position) {
        resolve({
          data: {
            lat: position.coords.latitude,
            lon: position.coords.longitude,
          },
        });
      }
      navigator.geolocation.getCurrentPosition(showPosition);
    } else {
      resolve(0);
    }
  });
  return promise;
}

export function showPaytmToast(value) {
  const bridgeName = getBridge();
  if (!isIosBuild() && typeof bridgeName !== 'undefined') {
    bridgeName.call('paytmToast', {
      message: value,
      isShort: true,
    });
  } else {
    // eslint-disable-next-line no-alert
    alert(value);
  }
}

export const copyToClipboard = text => {
  const bridgeName = getBridge();
  if (bridgeName) {
    bridgeName.call('setClipboard', {
      text,
    });
    showPaytmToast(COPY_TO_CLIPBOARD_TEXT);
  }
};

// export function setStatusBarBgColor() {
//   let colorVal;
//   if (isDarkMode()) {
//     colorVal = colors.LightBlack;
//   } else {
//     colorVal = colors.White2;
//   }
//   if (isBridge() && typeof getBridge() !== 'undefined') {
//     getBridge().call('paytmChangeStatusBarColor', {
//       color: colorVal,
//     });
//   }
// }

// For IOS Only
export const paytmResetStatusBarBackgroundColor = () => {
  const bridgeName = getBridge();
  if (isBridge() && typeof bridgeName !== 'undefined') {
    bridgeName.call('paytmResetStatusBarBackgroundColor', {}, result => {
      log(JSON.stringify(result));
    });
  }
};

// TODO: REMOVE WHEN NATIVE ISSUE IS FIXED
export const paytmChangeStatusBarColorBridge = color => {
  return '';
  window.JSBridge.call(
    'paytmChangeStatusBarColor',
    {
      color,
      statusBarStyle: isDarkMode() ? 1 : 0,
    },
    () => {},
  );
};

export const paytmChangeBottomBarColorBridge = color => {
  const bridgeName = getBridge();
  if (isBridge() && typeof bridgeName !== 'undefined') {
    log('paytmChangeBottomBarColorBridge isDarkMode:', isDarkMode());
    window.JSBridge.call(
      'changeBottomBarColor',
      { lightBottomBar: !isDarkMode(), bottomBarColor: color },
      result => {
        log(JSON.stringify(result));
      },
    );
  }
};

export function resetStatusBarBgColor() {
  const bridgeName = getBridge();
  if (isBridge() && typeof bridgeName !== 'undefined') {
    bridgeName.call('paytmResetStatusBarBackgroundColor');
  }
}

export function getPaytmUserInfoFromBridge(callback, infoKeys) {
  getBridge().call(
    'paytmGetUserInfo',
    {
      infoKeys,
    },
    result => {
      callback(result);
    },
  );
}

export function getPMLoginType(callback) {
  getBridge().call('pmGetLoginType', result => {
    callback(result);
  });
}

export const openDeepLinkPaytmMoney = (url, data, clearBackStack = false) => {
  getBridge().call(
    'paytmNavigateTo',
    {
      navigateTo: 'openPmDeeplink',
      clearBackStack, // to clear whole back stack of application
      finishThis: false, // to finish this activity
      data: data || { data: url },
    },
    () => {
      // Logger(`openDeepLink result....${JSON.stringify(result)}`);
    },
  );
};

export const getDeviceIdFromFetchValueForKeys = callback => {
  getBridge().call(
    'paytmFetchValuesForKeys',
    {
      keys: ['ssoToken', 'deviceId'], // keys array
    },
    result => {
      log(`paytmFetchValuesForKeys....bridge value${JSON.stringify(result)}`);
      callback((result && result.data) || {});
      // result-> {"data":{"name":"XYZ","ssoToken":"sdljkcjk8od787iumcnbui"}}
    },
  );
};

export const setDeviceID = () => {
  DeviceInfoProvider.setInfo('deviceId', getDeviceImeiFromBridge());
  if (isPaytmMoney()) {
    getDeviceIdFromFetchValueForKeys(data => {
      log(`device id value......${JSON.stringify(data)}`);
      if (data) {
        DeviceInfoProvider.setInfo(
          'deviceId',
          String(get(data, 'deviceId', '')),
        );
      } else {
        DeviceInfoProvider.setInfo('deviceId', getDeviceImeiFromBridge());
      }
    });
  } else {
    log(`paytm app, get from imei bridge${getDeviceImeiFromBridge()}`);
    DeviceInfoProvider.setInfo('deviceId', getDeviceImeiFromBridge());
  }
};

export const openPaytmMoneyPaymentAndGenericWebActivity = (
  postData,
  callback,
) => {
  const bridgeName = getBridge();
  if (bridgeName) {
    getBridge().call(
      'paytmNavigateTo',
      {
        navigateTo: 'pm_pg',
        data: postData,
      },
      result => {
        if (callback) {
          log(
            `openPaytmMoneyPaymentAndGenericWebActivity bridge value....${JSON.stringify(
              result,
            )}`,
          );
          callback(result);
        }
      },
    );
  }
};

export const paytmPushWindow = url => {
  getBridge().call('paytmPushWindow', {
    appId: '8cc7a6ac4e344a17a7b3ff069929b557',
    url,
    param: {
      showTitleBar: false,
    },
  });
};

export const getAppTheme = callback => {
  const bridgeName = getBridge();
  if (bridgeName) {
    bridgeName.call('getAppTheme', result => {
      if (callback) {
        callback(result);
      }
    });
  }
};

export const notifyNativeApp = postData => {
  const bridgeName = getBridge();
  if (bridgeName) {
    bridgeName.call(
      'notifyNativeApp',
      {
        data: postData,
      },
      result => result,
    );
  }
};

function deleteCookie(name) {
  const expires = new Date().toUTCString();
  const path = '/';
  const cookie = `${name}=; expires=${expires}; path=${path}; domain=.paytmmoney.com`;
  const cookieOnCurrentDomain = `${name}=; expires=${expires}; path=${path};`;
  document.cookie = cookie;
  document.cookie = cookieOnCurrentDomain;
}

export const refetch2FA = () => {
  if (isH5()) {
    const bridgeName = getBridge();
    if (
      typeof bridgeName === 'undefined' ||
      DeviceInfoProvider.getInfo('isRefresh2FAPending')
    ) {
      return;
    }

    DeviceInfoProvider.setInfo('isRefresh2FAPending', true);
    if (isH5()) {
      showPaytmToast(SESSION_EXPIRED_MSG);
    }
    bridgeName.call(
      'paytmNavigateTo',
      {
        navigateTo: 'refresh_2FA_token',
        data: {},
      },
      result => {
        DeviceInfoProvider.setInfo('isRefresh2FAPending', false);

        if (result?.data?.['x-2fa-token']) {
          const queryParams = {
            ...getQueryParamsFromString(getInitialQueryParams()),
            ...getQueryParams(),
          };

          const url = `${window.location.pathname}${generateQueryParamsString(
            queryParams,
          )}`;

          window.location.replace(url);
        } else {
          // show error page
          navigateTo(history, URL.ERROR_PAGE, {}, 'replace');
        }
      },
    );
  } else {
    deleteCookie(COOKIES.TWOFA_TOKEN);
    window.location.href = `${
      window.location.origin
    }/stocks/passcode?returnUrl=${encodeURIComponent(window.location.href)}`;
  }
};
