import { requestGenerator } from '../requests';
import { generateFeeds } from './dataConfig';
import EventHandler from '../dataConfig/EventHandler';
import Subscription from './Subscription';
import WebWorker from '../workers';

export const eventTypes = {
  destroyKey: 'destroyKey',
  addSubscriber: 'addSubscriber',
  createSourceObservable: 'createSourceObservable',
  newPacket: 'newPacket',
  connected: 'connected',
  reConnected: 'reConnected',
  connectionClosed: 'connectionClosed',
  connectionError: 'connectionError',
};

export default class SubscriptionManager extends EventHandler {
  constructor(
    url,
    decodeFn,
    options,
    apis = {},
    cachedApisKey,
    lastPacketSubject,
  ) {
    super(eventTypes);
    this.url = url;
    this.options = options;
    this.subscriptions = {};
    this.handlers = {};
    this.feed = null;
    this.dataStream = {};
    const { feedMap, feedArray } = generateFeeds();
    this.feedMap = feedMap;
    this.feedArray = feedArray;
    this.decode = decodeFn;
    this.apis = apis;
    this.cachedApisKey = cachedApisKey;
    this.heartBeatIntervalId = null;
    this.lastPacketIntervalId = null;
    this.lastPacketSubject = lastPacketSubject;

    this.authorize = this.authorize.bind(this);
    this.handleMessage = this.handleMessage.bind(this);
    this.handleClose = this.handleClose.bind(this);
    this.handleError = this.handleError.bind(this);
    this.destroyKey = this.destroyKey.bind(this);
    this.pauseConnection = this.pauseConnection.bind(this);
    this.reInitializeSocket = this.reInitializeSocket.bind(this);
  }

  initializeStream(
    authSettings = this.authSettings,
    inputOptions = this.options,
  ) {
    this.authSettings = authSettings;
    this.options = inputOptions;
    this.triggerEventHandlers(this.eventTypes.createSourceObservable, {
      url: this.url,
      started: true,
    });
    const {
      url,
      options,
      decode,
      apis,
      cachedApisKey,
      authorize: onOpen,
      handleClose: onClose,
      handleMessage: onMessage,
      handleError: onError,
    } = this;
    this.feed = new WebWorker(
      {
        url,
        options,
        decode,
        apis,
        cachedApisKey,
      },
      {
        onOpen,
        onClose,
        onMessage,
        onError,
      },
    );
  }

  closeSocket() {
    const openSubscriptions = Object.keys(this.subscriptions);
    if (openSubscriptions.length) {
      // console.warn(
      //   `Unsubscribe to existing open subscriptions: ${JSON.stringify(
      //     openSubscriptions,
      //   )}`,
      // );
      return false;
    }

    return this.feed.closeSocket();
  }

  pauseConnection() {
    this.feed.pauseConnection();
  }

  reconnectSocket() {
    this.feed.reconnectSocket();
  }

  authorize(data, isReconnect) {
    console.log('broadcast authenticate');
    this.feed.sendRequest('authenticate', this.authSettings);
    if (isReconnect) {
      this.triggerEventHandlers(this.eventTypes.reConnected, {
        url: this.url,
        authSettings: this.authSettings,
        data,
        isReconnect,
      });
    } else {
      this.triggerEventHandlers(this.eventTypes.connected, {
        url: this.url,
        authSettings: this.authSettings,
        data,
        isReconnect,
      });
    }
    if (this.heartBeatIntervalId)
      window.clearInterval(this.heartBeatIntervalId);
    this.heartBeatIntervalId = window.setInterval(() => {
      this.feed.sendRequest('heartbeat', []);
    }, 30000);
  }

  handleMessage(data) {
    this.triggerEventHandlers(this.eventTypes.newPacket, {
      url: this.url,
      data,
    });
    if (data && typeof data === 'object') {
      Object.entries(data).forEach(([key, value]) => {
        // log('handle message', key, value);
        if (this.feedMap[key]) {
          this.feedMap[key].next(value);
        }
      });
      if (this.lastPacketIntervalId)
        window.clearInterval(this.lastPacketIntervalId);
      if (this.lastPacketSubject) {
        this.lastPacketIntervalId = window.setInterval(() => {
          this.lastPacketSubject.next('No Packet');
        }, 5000);
      }
    }
  }

  handleClose(data) {
    console.log('broadcast connectionClosed');
    this.triggerEventHandlers(this.eventTypes.connectionClosed, {
      url: this.url,
      data,
      options: this.options,
    });
  }

  handleError(error) {
    this.triggerEventHandlers(this.eventTypes.connectionError, {
      url: this.url,
      error,
    });
  }

  getStream(type, inputPayload, requestedFeeds) {
    const [
      defaultFeeds,
      requestFeeds,
      request,
      storeInitialValue,
      payload,
    ] = requestGenerator[type](this.feedArray, inputPayload);
    const feeds = Array.isArray(requestedFeeds) ? requestedFeeds : defaultFeeds;

    if (feeds.length === 0) {
      return feeds;
    }

    const key = `${type}_${JSON.stringify(payload)}`;

    if (!this.subscriptions[key]) {
      this.subscriptions[key] = new Subscription(
        requestFeeds,
        request,
        storeInitialValue,
        payload,
        this.destroyKey(key),
      );

      /**
       * Send subscribe request
       */
      this.feed.sendRequest(request.subscribe, payload);
      this.triggerEventHandlers(this.eventTypes.addSubscriber, {
        url: this.url,
        key,
        feeds,
        createKey: true,
      });
    } else {
      this.triggerEventHandlers(this.eventTypes.addSubscriber, {
        url: this.url,
        key,
        feeds,
        createKey: false,
      });
    }

    return this.subscriptions[key].addSubscriber(feeds);
  }

  reInitializeSocket() {
    this.feed.reInitializeSocket();
  }

  destroyKey(key) {
    return () => {
      if (this.subscriptions[key].subscriberCount === 0) {
        const { unsubscribe, payload } = this.subscriptions[key];

        /**
         * Send unsubscribe request
         */
        this.feed.sendRequest(unsubscribe, payload);
        this.triggerEventHandlers(this.eventTypes.destroyKey, {
          url: this.url,
          key,
        });
        delete this.subscriptions[key];
      }
    };
  }
}
