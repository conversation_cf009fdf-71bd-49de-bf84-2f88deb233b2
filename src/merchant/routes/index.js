import { MERCHANT_AUTHORIZATION } from '../config/urlConfig';

const merchantRoutes = [
  {
    path: MERCHANT_AUTHORIZATION.GET_STARTED,
    load: () =>
      import(
        /* webpackChunkname: 'MerchantAuthorization' */ './GetStartedLanding'
      ),
  },
  {
    path: MERCHANT_AUTHORIZATION.STRATEGY_LANDING,
    load: () =>
      import(
        /* webpackChunkname: 'MerchantAuthorization' */ './StrategyLandingPage'
      ),
  },
];

export default merchantRoutes;
