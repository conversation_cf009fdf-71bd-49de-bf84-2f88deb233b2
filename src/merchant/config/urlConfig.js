import { PF_AUTH_HOST, PF_HOST } from '../../config/urlConfig';

export const MERCHANT_AUTHORIZATION = {
  GET_STARTED: '/merchant-authorization',
  STRATEGY_LANDING: '/strategy-landing-dashboard',
};

export const MERCHANT_API_URL = {
  MERCHANT_AUTH_TOKEN: (
    deviceId,
    deviceManufacturer,
    deviceName,
    client,
    version,
    osVersion,
  ) =>
    `${PF_AUTH_HOST}api/auth/generate-merchant-jwt?deviceIdentifier=${deviceId}&deviceManufacturer=${deviceManufacturer}&deviceName=${deviceName}&client=${client}&version=${version}&osVersion=${osVersion}`,
  VALIDATE_PASSCODE: requestToken =>
    `${PF_HOST}merchantonboarding/v1/token/request/${requestToken}/passcode/valid`,
  AUTHORIZE_REQUEST_TOKEN: () =>
    `${PF_HOST}merchant-auth/authorisation/v1/user/merchant/permissions/authorise`,
  GET_MERCHANT_PERMISSIONS: requestToken =>
    `${PF_HOST}merchant-auth/authorisation/v1/user/permissions?requestToken=${requestToken}`,
  CHECK_LOGIN_TYPE: userId => `${PF_HOST}2fa/totp/user/${userId}`,
  SEND_OTP: userId => `${PF_HOST}2fa/totp/user/${userId}/otp/send`,
  VERIFY_OTP: requestToken =>
    `${PF_HOST}merchantonboarding/v1/token/request/${requestToken}/otp/valid`,
  VERIFY_TOTP: requestToken =>
    `${PF_HOST}merchantonboarding/v1/token/request/${requestToken}/totp/valid`,
  GET_TRADERON_COHORT: () => `${PF_AUTH_HOST}api/agg/getAccess`,
};

export const MERCHANT_API_MASKED_URL = {
  VALIDATE_PASSCODE: `${PF_HOST}merchantonboarding/v1/token/request/######/passcode/valid`,
  GET_MERCHANT_PERMISSIONS: `${PF_HOST}merchant-auth/authorisation/v1/user/permissions?requestToken=######`,
  VERIFY_OTP: `${PF_HOST}merchantonboarding/v1/token/request/######/otp/valid`,
  VERIFY_TOTP: `${PF_HOST}merchantonboarding/v1/token/request/######/totp/valid`,
};
