import React, { useEffect, useState } from 'react';

import HeaderWithTabs from './_partials/HeaderWithTabs/index';

import TradingIdeasListBody from './_partials/TradingIdeasListBody/TradingIdeasListBody';

import { useTradingIdeasContext } from '../../../context/TradingIdeasContext';

import {
  exitApp,
  paytmChangeBottomBarColorBridge,
  paytmChangeStatusBarColorBridge,
} from '../../../../utils/bridgeUtils';
import { useBackPress } from '../../../../utils/react';
import { getDeeplinkDataOrQueryParam, isIosBuild } from '../../../../utils/commonUtils';

import styles from './index.scss';

function TradingIdeasListWrapperPage({
  isRefreshIcon,
  handleTabChange,
  getTradingIdeas,
  isBackIcon,
  onBackIconClick,
  isHamburgerIcon,
  title,
  tabs,
  ...props
}) {
  const { activeTab, setActiveTab } = useTradingIdeasContext(props);
  const { pushStack, clearStack } = useBackPress();
  const [ideaToSelect, setIdeaToSelect] = useState(null);

  const getURLData = () => {
    const tabToSelect = getDeeplinkDataOrQueryParam('tab');
    const callId = getDeeplinkDataOrQueryParam('callId');
    if (callId) {
      if (isIosBuild()) {
        const selectedIdea = callId?.split('?')?.[0];
        setIdeaToSelect(selectedIdea);
      } else {
        setIdeaToSelect(callId);
      }
    }

    if (tabToSelect) {
      if (isIosBuild()) {
        const selectedTab = tabToSelect?.split('?')?.[0];
        setActiveTab(selectedTab);
      } else {
        setActiveTab(tabToSelect);
      }
    }
  };

  const handleBackPress = () => {
    exitApp();
  };

  useEffect(() => {
    const style = getComputedStyle(document.body);
    // TODO: REMOVE WHEN NATIVE ISSUE IS FIXED
    paytmChangeBottomBarColorBridge(
      style.getPropertyValue('--pml-eq-primary-color'),
    );
    setTimeout(() => {
      paytmChangeStatusBarColorBridge(
        style.getPropertyValue('--pml-eq-grey-tertiary'),
      );
    }, 100);
    getURLData();
    pushStack(handleBackPress);
    return () => {
      clearStack();
    };
  }, []);
  return (
    <div className={styles.container}>
      <HeaderWithTabs
        isRefreshIcon={isRefreshIcon}
        onRefreshIconClick={getTradingIdeas}
        isBackIcon={isBackIcon}
        onBackIconClick={onBackIconClick}
        isHamburgerIcon={isHamburgerIcon}
        title={title}
        tabs={tabs}
        activeTab={activeTab}
        handleTabChange={handleTabChange}
        customClass={styles.headerCustomClass}
        tabContainerCustomClass={styles.tabContainerCustomClass}
      />
      <TradingIdeasListBody activeTab={activeTab} ideaToSelect={ideaToSelect} />
    </div>
  );
}

export default TradingIdeasListWrapperPage;
