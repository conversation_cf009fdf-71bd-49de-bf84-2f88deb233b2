import React from 'react';
import classNames from 'classnames';

import { EXIT_AT } from '../../config/lotusTradingConfig';

import { isBuyBracketButtonVisible } from '../../config/tradingIdeasUtils';
import {
  bracketOrderButton,
  buySellButton,
  intradayButton,
} from '../../components/BuySellCTA';

import styles from './index.scss';

const CardFooter = ({
  isBuy,
  activeTab,
  status,
  tag1,
  exit,
  customClass,
  ...data
}) => {
  const renderExit = () =>
    exit.isExit ? (
      <div className={styles.exit}>
        <span>{EXIT_AT} </span>
        {exit.value}
      </div>
    ) : null;

  return (
    <div className={classNames(styles.footer, customClass)}>
      {renderExit()}
      <div className={styles.btnContainer}>
        {intradayButton(isBuy, status, tag1, activeTab, data)}
        {isBuyBracketButtonVisible(activeTab, tag1, status) ? (
          bracketOrderButton(isBuy, activeTab, data)
        ) : (
          <div />
        )}
        {buySellButton(activeTab, isBuy, status, data)}
      </div>
    </div>
  );
};

export default CardFooter;
