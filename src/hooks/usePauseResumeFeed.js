import { useEffect, useRef, useCallback } from 'react';

import { useDocumentHide } from '../components/App';

function usePauseResumeFeed({ isDataFeedReady, dataFeed, setIsDataFeedReady }) {
  const broadCastRef = useRef({
    dataFeedRef: false,
    isDataFeedReadyRef: false,
  });

  useEffect(() => {
    // update broadcast status ref
    broadCastRef.current.isDataFeedReadyRef = isDataFeedReady;
    broadCastRef.current.dataFeedRef = dataFeed;
  }, [isDataFeedReady, dataFeed]);

  const pauseFeed = () => {
    console.log('broadcast feed paused');
    if (
      broadCastRef.current.dataFeedRef &&
      broadCastRef.current.isDataFeedReadyRef
    ) {
      setIsDataFeedReady(false);
      broadCastRef.current.dataFeedRef.pauseConnection();
    }
  };

  const resumeFeed = useCallback(() => {
    console.log('broadcast feed resumed');
    if (
      broadCastRef.current.dataFeedRef &&
      !broadCastRef.current.isDataFeedReadyRef
    ) {
      broadCastRef.current.dataFeedRef.reInitializeSocket();
      setIsDataFeedReady(true);
    }
  }, [dataFeed, isDataFeedReady]);

  useDocumentHide({
    onPause: pauseFeed,
    onResume: resumeFeed,
  });
}

export { usePauseResumeFeed };
